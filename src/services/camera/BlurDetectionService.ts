/**
 * BlurDetectionService provides real-time blur detection for camera frames
 * using Laplacian variation algorithm with react-native-worklets-core
 */

import { Worklets } from 'react-native-worklets-core';
import type { Frame } from 'react-native-vision-camera';

export interface BlurDetectionResult {
  isBlurry: boolean;
  blurScore: number;
  threshold: number;
}

export interface BlurDetectionConfig {
  threshold?: number;
  sampleSize?: number;
  enableLogging?: boolean;
  skipFrames?: number; // Skip N frames between processing for performance
}

/**
 * Default configuration for blur detection
 */
const DEFAULT_CONFIG: Required<BlurDetectionConfig> = {
  threshold: 100, // Lower values = more sensitive to blur
  sampleSize: 100, // Size to resize frame for processing (100x100)
  enableLogging: false,
  skipFrames: 2, // Process every 3rd frame by default
};

/**
 * Worklet function to calculate Laplacian variance for blur detection
 * This runs on the worklet thread for optimal performance
 */
function calculateLaplacianVariance(
  imageData: Uint8Array,
  width: number,
  height: number,
  threshold: number,
  enableLogging: boolean,
): BlurDetectionResult {
  'worklet';

  try {
    // Convert to grayscale if needed (assuming RGBA format)
    const grayscale = new Array(width * height);
    for (let i = 0; i < width * height; i++) {
      const pixelIndex = i * 4; // RGBA
      // Grayscale conversion using luminance formula
      grayscale[i] = Math.round(
        0.299 * imageData[pixelIndex] + // R
          0.587 * imageData[pixelIndex + 1] + // G
          0.114 * imageData[pixelIndex + 2], // B
      );
    }

    // Apply Laplacian kernel
    const laplacian = new Array(width * height).fill(0);

    // Laplacian kernel: [0, -1, 0; -1, 4, -1; 0, -1, 0]
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x;
        laplacian[idx] =
          -1 * grayscale[(y - 1) * width + x] + // top
          -1 * grayscale[y * width + (x - 1)] + // left
          4 * grayscale[y * width + x] + // center
          -1 * grayscale[y * width + (x + 1)] + // right
          -1 * grayscale[(y + 1) * width + x]; // bottom
      }
    }

    // Calculate variance of Laplacian
    const mean =
      laplacian.reduce((sum, val) => sum + val, 0) / laplacian.length;
    const variance =
      laplacian.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) /
      laplacian.length;

    const isBlurry = variance < threshold;

    if (enableLogging) {
      console.log(
        `BlurDetectionService.ts: calculateLaplacianVariance(): Variance: ${variance.toFixed(2)}, Threshold: ${threshold}, Blurry: ${isBlurry}`,
      );
    }

    return {
      isBlurry,
      blurScore: variance,
      threshold,
    };
  } catch (error) {
    if (enableLogging) {
      console.error(
        'BlurDetectionService.ts: calculateLaplacianVariance():',
        error,
      );
    }
    return {
      isBlurry: false,
      blurScore: 0,
      threshold,
    };
  }
}

/**
 * BlurDetectionService class for managing blur detection functionality
 */
export class BlurDetectionService {
  private config: Required<BlurDetectionConfig>;
  private frameCounter: number = 0;

  constructor(config: BlurDetectionConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Update the blur detection configuration
   */
  updateConfig(newConfig: Partial<BlurDetectionConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<BlurDetectionConfig> {
    return { ...this.config };
  }

  /**
   * Process a camera frame for blur detection
   * This is the main method to be called from frame processors
   */
  processFrame(
    imageData: Uint8Array,
    width: number,
    height: number,
  ): BlurDetectionResult | null {
    // Validate input parameters
    if (!imageData || imageData.length === 0 || width <= 0 || height <= 0) {
      return null;
    }

    // Check if imageData has the expected size (width * height * 4 for RGBA)
    const expectedSize = width * height * 4;
    if (imageData.length < expectedSize) {
      return null;
    }

    // Skip frames for performance
    this.frameCounter++;
    if (this.frameCounter % (this.config.skipFrames + 1) !== 0) {
      return null;
    }

    try {
      // Resize frame for faster processing if needed
      const { resizedData, resizedWidth, resizedHeight } = this.resizeFrame(
        imageData,
        width,
        height,
        this.config.sampleSize,
      );

      const result = calculateLaplacianVariance(
        resizedData,
        resizedWidth,
        resizedHeight,
        this.config.threshold,
        this.config.enableLogging,
      );

      return result;
    } catch (error) {
      if (this.config.enableLogging) {
        console.error('BlurDetectionService.ts: processFrame():', error);
      }
      return null;
    }
  }

  /**
   * Resize frame data for faster processing
   * Simple nearest-neighbor downsampling
   */
  private resizeFrame(
    imageData: Uint8Array,
    originalWidth: number,
    originalHeight: number,
    targetSize: number,
  ): { resizedData: Uint8Array; resizedWidth: number; resizedHeight: number } {
    // Calculate new dimensions maintaining aspect ratio
    const aspectRatio = originalWidth / originalHeight;
    let newWidth: number;
    let newHeight: number;

    if (aspectRatio > 1) {
      newWidth = targetSize;
      newHeight = Math.round(targetSize / aspectRatio);
    } else {
      newHeight = targetSize;
      newWidth = Math.round(targetSize * aspectRatio);
    }

    const resizedData = new Uint8Array(newWidth * newHeight * 4); // RGBA
    const scaleX = originalWidth / newWidth;
    const scaleY = originalHeight / newHeight;

    for (let y = 0; y < newHeight; y++) {
      for (let x = 0; x < newWidth; x++) {
        const srcX = Math.floor(x * scaleX);
        const srcY = Math.floor(y * scaleY);
        const srcIndex = (srcY * originalWidth + srcX) * 4;
        const destIndex = (y * newWidth + x) * 4;

        // Copy RGBA values
        resizedData[destIndex] = imageData[srcIndex]; // R
        resizedData[destIndex + 1] = imageData[srcIndex + 1]; // G
        resizedData[destIndex + 2] = imageData[srcIndex + 2]; // B
        resizedData[destIndex + 3] = imageData[srcIndex + 3]; // A
      }
    }

    return {
      resizedData,
      resizedWidth: newWidth,
      resizedHeight: newHeight,
    };
  }

  /**
   * Create a frame processor function for use with react-native-vision-camera
   */
  createFrameProcessor(
    onBlurDetected: (result: BlurDetectionResult) => void,
  ): (frame: Frame) => void {
    const config = this.config;
    let frameCounter = 0;

    // Create the worklet function that will run on the camera thread
    const processFrameWorklet = (frame: Frame) => {
      'worklet';

      try {
        // Skip frames for performance
        frameCounter++;
        if (frameCounter % (config.skipFrames + 1) !== 0) {
          return;
        }

        // Check if frame has the required properties
        if (!frame || !frame.width || !frame.height) {
          return;
        }

        // Extract image data from frame
        const imageData = this.extractImageDataFromFrame(frame);

        if (!imageData) {
          return;
        }

        // Resize frame for faster processing
        const { resizedData, resizedWidth, resizedHeight } = this.resizeFrame(
          imageData,
          frame.width,
          frame.height,
          config.sampleSize,
        );

        // Calculate blur detection
        const result = calculateLaplacianVariance(
          resizedData,
          resizedWidth,
          resizedHeight,
          config.threshold,
          config.enableLogging,
        );

        if (result) {
          // Use runOnJS to call the callback on the JS thread
          const runOnJSCallback = Worklets.createRunOnJS(onBlurDetected);
          runOnJSCallback(result);
        }
      } catch (error) {
        if (config.enableLogging) {
          const runOnJSError = Worklets.createRunOnJS((err: any) => {
            console.error(
              'BlurDetectionService.ts: createFrameProcessor():',
              err,
            );
          });
          runOnJSError(error);
        }
      }
    };

    return processFrameWorklet;
  }

  /**
   * Extract image data from vision-camera frame
   * This method converts the frame to RGBA format for processing
   */
  private extractImageDataFromFrame(frame: Frame): Uint8Array | null {
    'worklet';

    try {
      // Validate frame object
      if (!frame || typeof frame.toArrayBuffer !== 'function') {
        if (this.config.enableLogging) {
          console.warn(
            'BlurDetectionService.ts: extractImageDataFromFrame(): Invalid frame object',
          );
        }
        return null;
      }

      // Use the frame's toArrayBuffer method to get pixel data
      const buffer = frame.toArrayBuffer();

      if (!buffer) {
        if (this.config.enableLogging) {
          console.warn(
            'BlurDetectionService.ts: extractImageDataFromFrame(): Frame buffer is null',
          );
        }
        return null;
      }

      const data = new Uint8Array(buffer);

      if (data.length === 0) {
        if (this.config.enableLogging) {
          console.warn(
            'BlurDetectionService.ts: extractImageDataFromFrame(): Empty frame data',
          );
        }
        return null;
      }

      // The frame format determines how we interpret the data
      // For RGB frames, data is already in the right format
      // For YUV frames, we'd need conversion (not implemented here)
      if (frame.pixelFormat === 'rgb') {
        return data;
      } else {
        // For other formats, we'll need to implement conversion
        // For now, return null to skip processing
        if (this.config.enableLogging) {
          const runOnJSWarn = Worklets.createRunOnJS((format: string) => {
            console.warn(
              `BlurDetectionService.ts: extractImageDataFromFrame(): Unsupported pixel format: ${format}`,
            );
          });
          runOnJSWarn(frame.pixelFormat);
        }
        return null;
      }
    } catch (error) {
      if (this.config.enableLogging) {
        const runOnJSError = Worklets.createRunOnJS((err: any) => {
          console.error(
            'BlurDetectionService.ts: extractImageDataFromFrame():',
            err,
          );
        });
        runOnJSError(error);
      }
      return null;
    }
  }
}

/**
 * Default instance for easy usage
 */
export const blurDetectionService = new BlurDetectionService();
